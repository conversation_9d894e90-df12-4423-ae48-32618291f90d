package com.ict.ycwl.pathcalculate.service;

import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.pathcalculate.pojo.Version;
import com.ict.ycwl.pathcalculate.pojo.dto.VersionDTO;

import java.util.List;
import java.util.Map;

public interface VersionService {
    AjaxResult selectById(int version);

    List<Version> selectList();

    AjaxResult saveNewVersion(VersionDTO versionDTO);

    AjaxResult deleteVersion(int versionId);


    Version selectOne();

    AjaxResult saveNew(String version);

    AjaxResult saveNew(String version, String apiKey);

    AjaxResult master(String master);

    int update(Version version);

    Map<Integer,Boolean> isDifferent();
}
