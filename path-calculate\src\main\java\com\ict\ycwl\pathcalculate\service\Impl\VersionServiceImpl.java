package com.ict.ycwl.pathcalculate.service.Impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.pathcalculate.AOP.DynamicDS;
import com.ict.ycwl.pathcalculate.mapper.VersionMapper;
import com.ict.ycwl.pathcalculate.pojo.Version;
import com.ict.ycwl.pathcalculate.pojo.dto.VersionDTO;
import com.ict.ycwl.pathcalculate.pojo.dynamiEntity.*;
import com.ict.ycwl.pathcalculate.service.VersionService;
import com.ict.ycwl.pathcalculate.utils.dbDataSourceUtils.FileUtil;
import com.ict.ycwl.pathcalculate.utils.dbDataSourceUtils.FileWriteUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j(topic = "c.txt")
public class VersionServiceImpl implements VersionService {

    @Value("${jjking.dbPath}")
    private String dbPath;

    @Autowired
    private VersionMapper versionMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private SaveVersionServiceImpl service;


    @Override
    public AjaxResult selectById(int version) {
        String versionDb = versionMapper.selectById(version);
        System.out.println("内容：" + versionDb);
        if (StringUtil.isBlank(versionDb)) {
            return AjaxResult.error("版本已被删除，切换失败");
        }
        //2.修改磁盘中记录的版本文件内容
        boolean b = FileWriteUtil.writeToFile(dbPath, versionDb);
        //3.返回修改情况
        if (b) {
            return AjaxResult.success("切换成功");
        }
        return AjaxResult.error("切换失败");
    }

    @Override
    @DS("master")
    public List<Version> selectList() {
        return versionMapper.selectList();
    }

    @Override
    public AjaxResult saveNewVersion(VersionDTO versionDTO) {
        //插入到数据库中
        int i = versionMapper.updateById(versionDTO.getVersionId(), versionDTO.getVersionName(), versionDTO.getVersionInfo());
        if (i == 1) {
            return AjaxResult.success("版本信息修改成功");
        }
        return AjaxResult.error("版本信息修改失败");
    }

    @Override
    public AjaxResult deleteVersion(int versionId) {
        int i = versionMapper.deleteIsShowById(versionId);
        if (i == 1) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.error("删除失败");
    }

    @Override
    @DS("master")
    public Version selectOne() {
        Version version = versionMapper.MySelectOne();
        return version;
    }

    @Override
    public AjaxResult saveNew(String version) {
        // 调用带API密钥的重载方法，使用默认API密钥
        return saveNew(version, "a123fae9da370c45984c58720bf3ac7c");
    }

    @Override
    public AjaxResult saveNew(String version, String apiKey) {
        // 1. Accumulation 表
        List<AccumulationF> accumulations = jdbcTemplate.query(
                "select * from accumulation where is_delete=0",
                new BeanPropertyRowMapper<>(AccumulationF.class)
        );
// 3. Area 表
        List<AreaF> areas = jdbcTemplate.query(
                "select * from area",
                new BeanPropertyRowMapper<>(AreaF.class)
        );
// 4. Car 表
        List<CarF> cars = jdbcTemplate.query(
                "select * from car",
                new BeanPropertyRowMapper<>(CarF.class)
        );
// 7. Delivery_area 表
        List<DeliveryAreaF> deliveryAreas = jdbcTemplate.query(
                "select * from delivery_area",
                new BeanPropertyRowMapper<>(DeliveryAreaF.class)
        );

// 9. Dist 表
        List<DistF> dists = jdbcTemplate.query(
                "select * from dist",
                new BeanPropertyRowMapper<>(DistF.class)
        );

// 10. Error_point 表
        List<ErrorPointF> errorPointFS = jdbcTemplate.query(
                "select * from error_point",
                new BeanPropertyRowMapper<>(ErrorPointF.class)
        );
        // 12. Feedback 表
        List<FeedbackF> feedbackFS = jdbcTemplate.query(
                "select * from feedback",
                new BeanPropertyRowMapper<>(FeedbackF.class)
        );
        // 13. Feedback_file 表
        List<FeedbackFileF> feedbackFileFS = jdbcTemplate.query(
                "select * from feedback_file",
                new BeanPropertyRowMapper<>(FeedbackFileF.class)
        );
// 14. Feedback_reply 表
        List<FeedbackReplyF> feedbackReplyFS = jdbcTemplate.query(
                "select * from feedback_reply",
                new BeanPropertyRowMapper<>(FeedbackReplyF.class)
        );

// 15. Feedback_reply_file 表
        List<FeedbackReplyFileF> feedbackReplyFileFS = jdbcTemplate.query(
                "select * from feedback_reply_file",
                new BeanPropertyRowMapper<>(FeedbackReplyFileF.class)
        );
// 16. File_import_logs 表
        List<FileImportLogsF> fileImportLogsFS = jdbcTemplate.query(
                "select * from file_import_logs",
                new BeanPropertyRowMapper<>(FileImportLogsF.class)
        );
// 17. Gear 表
        List<GearF> gearves = jdbcTemplate.query(
                "select * from gear",
                new BeanPropertyRowMapper<>(GearF.class)
        );

        // 18. Group 表
        List<GroupF> groupFS = jdbcTemplate.query(
                "select * from `group`",
                new BeanPropertyRowMapper<>(GroupF.class)
        );

        // 19. Group_areas 表
        List<GroupAreasF> groupAreasFS = jdbcTemplate.query(
                "select * from group_areas",
                new BeanPropertyRowMapper<>(GroupAreasF.class)
        );

// 20. Operation 表
        List<OperationF> operationFS = jdbcTemplate.query(
                "select * from operation",
                new BeanPropertyRowMapper<>(OperationF.class)
        );

// 21. Pickup_user 表
        List<PickupUserF> pickupUserFS = jdbcTemplate.query(
                "select * from pickup_user",
                new BeanPropertyRowMapper<>(PickupUserF.class)
        );

// 22. Pickup_user_import 表
        List<PickupUserImportF> pickupUserImportFS = jdbcTemplate.query(
                "select * from pickup_user_import",
                new BeanPropertyRowMapper<>(PickupUserImportF.class)
        );

// 23. Pickup_user_parameter 表
        List<PickupUserParameterF> pickupUserParameterFS = jdbcTemplate.query(
                "select * from pickup_user_parameter",
                new BeanPropertyRowMapper<>(PickupUserParameterF.class)
        );

// 24. Point_distance 表
        List<PointDistanceF> pointDistanceFS = jdbcTemplate.query(
                "select * from point_distance",
                new BeanPropertyRowMapper<>(PointDistanceF.class)
        );

// 25. Role 表
        List<RoleF> roleFS = jdbcTemplate.query(
                "select * from role",
                new BeanPropertyRowMapper<>(RoleF.class)
        );

// 26. Role_operation 表
        List<RoleOperationF> roleOperationFS = jdbcTemplate.query(
                "select * from role_operation",
                new BeanPropertyRowMapper<>(RoleOperationF.class)
        );

// 27. Route 表
        List<RouteF> routeFS = jdbcTemplate.query(
                "select * from route where is_delete=0",
                new BeanPropertyRowMapper<>(RouteF.class)
        );
// 31. Route_detail 表
        List<RouteDetailF> routeDetailves = jdbcTemplate.query(
                "select * from route_detail",
                new BeanPropertyRowMapper<>(RouteDetailF.class)
        );

// 32. Route_user 表
        List<RouteUserF> routeUserFS = jdbcTemplate.query(
                "select * from route_user",
                new BeanPropertyRowMapper<>(RouteUserF.class)
        );

// 33. Scheduling 表
        List<SchedulingF> schedulingFS = jdbcTemplate.query(
                "select * from scheduling",
                new BeanPropertyRowMapper<>(SchedulingF.class)
        );


// 36. Site_selection 表
        List<SiteSelectionF> siteSelectionFS = jdbcTemplate.query(
                "select * from site_selection",
                new BeanPropertyRowMapper<>(SiteSelectionF.class)
        );

// 37. Site_store 表
        List<SiteStoreF> siteStoreFS = jdbcTemplate.query(
                "select * from site_store",
                new BeanPropertyRowMapper<>(SiteStoreF.class)
        );

// 38. Store 表
        List<StoreF> storeFS = jdbcTemplate.query(
                "select * from store where is_delete=0",
                new BeanPropertyRowMapper<>(StoreF.class)
        );
// 41. Store_time 表
        List<StoreTimeF> storeTimeFS = jdbcTemplate.query(
                "select * from store_time",
                new BeanPropertyRowMapper<>(StoreTimeF.class)
        );

// 42. Store_two 表
        List<StoreTwoF> storeTwoFS = jdbcTemplate.query(
                "select * from store_two",
                new BeanPropertyRowMapper<>(StoreTwoF.class)
        );

// 43. System_parameter 表
        List<SystemParameterF> systemParameterFS = jdbcTemplate.query(
                "select * from system_parameter",
                new BeanPropertyRowMapper<>(SystemParameterF.class)
        );

        List<TeamF> teamFS = jdbcTemplate.query(
                "select * from team",
                new BeanPropertyRowMapper<>(TeamF.class)
        );

        List<TransitDeliveryF> transitDeliveryFS = jdbcTemplate.query(
                "select * from transit_delivery",
                new BeanPropertyRowMapper<>(TransitDeliveryF.class)
        );

        List<TransitDepotF> transitDepotFS = jdbcTemplate.query(
                "select * from transit_depot",
                new BeanPropertyRowMapper<>(TransitDepotF.class)
        );

        List<TransitDepotCarF> transitDepotCarves = jdbcTemplate.query(
                "select * from transit_depot_car",
                new BeanPropertyRowMapper<>(TransitDepotCarF.class)
        );
        List<UserF> userFS = jdbcTemplate.query(
                "select * from user",
                new BeanPropertyRowMapper<>(UserF.class)
        );
        List<UserGroupF> userGroupFS = jdbcTemplate.query(
                "select * from user_group",
                new BeanPropertyRowMapper<>(UserGroupF.class)
        );
        // 8. 版本管理表
        List<VersionF> versionFS = jdbcTemplate.query(
                "SELECT * FROM version",
                new BeanPropertyRowMapper<>(VersionF.class));

        AjaxResult ajaxResult = null;
        if ("slave1".equals(version)) {
            ajaxResult = service.saveVersionSlave1(accumulations, areas, cars, deliveryAreas, dists, errorPointFS, feedbackFS, feedbackFileFS, feedbackReplyFS,
                    feedbackReplyFileFS, fileImportLogsFS, gearves, groupFS, groupAreasFS, operationFS, pickupUserFS, pickupUserImportFS,
                    pickupUserParameterFS, schedulingFS, siteSelectionFS, siteStoreFS, storeFS, storeTimeFS, storeTwoFS, systemParameterFS, teamFS
                    , transitDeliveryFS, transitDepotFS, transitDepotCarves, userFS, userGroupFS, versionFS, pointDistanceFS, roleFS, roleOperationFS, routeFS
                    , routeDetailves, routeUserFS);
        } else if ("slave2".equals(version)) {
            ajaxResult = service.saveVersionSlave2(accumulations, areas, cars, deliveryAreas, dists, errorPointFS, feedbackFS, feedbackFileFS, feedbackReplyFS,
                    feedbackReplyFileFS, fileImportLogsFS, gearves, groupFS, groupAreasFS, operationFS, pickupUserFS, pickupUserImportFS,
                    pickupUserParameterFS, schedulingFS, siteSelectionFS, siteStoreFS, storeFS, storeTimeFS, storeTwoFS, systemParameterFS, teamFS
                    , transitDeliveryFS, transitDepotFS, transitDepotCarves, userFS, userGroupFS, versionFS, pointDistanceFS, roleFS, roleOperationFS, routeFS
                    , routeDetailves, routeUserFS);
        } else {
            ajaxResult = service.saveVersionSlave3(accumulations, areas, cars, deliveryAreas, dists, errorPointFS, feedbackFS, feedbackFileFS, feedbackReplyFS,
                    feedbackReplyFileFS, fileImportLogsFS, gearves, groupFS, groupAreasFS, operationFS, pickupUserFS, pickupUserImportFS,
                    pickupUserParameterFS, schedulingFS, siteSelectionFS, siteStoreFS, storeFS, storeTimeFS, storeTwoFS, systemParameterFS, teamFS
                    , transitDeliveryFS, transitDepotFS, transitDepotCarves, userFS, userGroupFS, versionFS, pointDistanceFS, roleFS, roleOperationFS, routeFS
                    , routeDetailves, routeUserFS);
        }
        return ajaxResult;
    }

    @Override
    @DynamicDS("#master")
    public AjaxResult master(String master) {
        List<Version> versions = versionMapper.selectList();

        return AjaxResult.success(versions);
    }

    @Override
    @DS("master")
    public int update(Version version) {
        return versionMapper.MyUpdateById(version.getVersionId(), version.getVersionName(), version.getVersionInfo());
    }

    @Override
    public Map<Integer, Boolean> isDifferent() {

        List<Version> versions = versionMapper.selectAllList();

        int currVersionId = 1;
        String type = FileUtil.readSingleLine(dbPath);
        if ("slave1".equals(type)) {
            currVersionId = 2;
        } else if ("slave2".equals(type)) {
            currVersionId = 3;
        } else if ("slave3".equals(type)) {
            currVersionId = 4;
        }

        //1.在另一个service中写四个方法，对应四个数据库
        //2.先判断班组信息是否匹配
        List<TeamF> teamFS = jdbcTemplate.query("select * from team where is_delete=0", new BeanPropertyRowMapper<>(TeamF.class));
        System.out.println(teamFS);
        //3.判断对接点信息是否匹配
        List<TransitDepotF> transitDepotFS = jdbcTemplate.query("select * from transit_depot where is_delete=0", new BeanPropertyRowMapper<>(TransitDepotF.class));
        //4.判断配送域信息是否匹配
        List<DeliveryAreaF> deliveryAreaves = jdbcTemplate.query("select * from delivery_area where is_delete=0", new BeanPropertyRowMapper<>(DeliveryAreaF.class));
        //判断车辆信息是否匹配
        List<CarF> carves = jdbcTemplate.query("select * from car where is_delete=0", new BeanPropertyRowMapper<>(CarF.class));
        //判断商铺信息是否匹配
        List<StoreF> storeFS = jdbcTemplate.query("select * from store where is_delete=0", new BeanPropertyRowMapper<>(StoreF.class));

        HashMap<Integer, Boolean> map = new HashMap<>();
        if (currVersionId == 1) {
            if (versions.get(1).getIsShow() == 1) {
                boolean slave1 = service.checkVersionSlave1(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(2, slave1);
            }
            if (versions.get(2).getIsShow() == 1) {
                boolean slave2 = service.checkVersionSlave2(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(3, slave2);
            }
            if (versions.get(3).getIsShow() == 1) {
                boolean slave3 = service.checkVersionSlave3(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(4, slave3);
            }
        } else if (currVersionId == 2) {
            if (versions.get(0).getIsShow() == 1) {
                boolean master = service.checkVersionMaster(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(1, master);
            }
            if (versions.get(2).getIsShow() == 1) {
                boolean slave2 = service.checkVersionSlave2(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(3, slave2);
            }
            if (versions.get(3).getIsShow() == 1) {
                boolean slave3 = service.checkVersionSlave3(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(4, slave3);
            }

        } else if (currVersionId == 3) {
            if (versions.get(0).getIsShow() == 1) {
                boolean master = service.checkVersionMaster(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(1, master);
            }
            if (versions.get(1).getIsShow() == 1) {
                boolean slave1 = service.checkVersionSlave1(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(2, slave1);
            }
            if (versions.get(3).getIsShow() == 1) {
                boolean slave3 = service.checkVersionSlave3(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(4, slave3);
            }
        } else {
            if (versions.get(0).getIsShow() == 1) {
                boolean master = service.checkVersionMaster(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(1, master);
            }
            if (versions.get(1).getIsShow() == 1) {
                boolean slave1 = service.checkVersionSlave1(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(2, slave1);
            }
            if (versions.get(2).getIsShow() == 1) {
                boolean slave2 = service.checkVersionSlave2(teamFS, transitDepotFS, deliveryAreaves, carves, storeFS);
                map.put(3, slave2);
            }
        }
        Set<Integer> integers = map.keySet();
        for (Integer integer : integers) {
            System.out.println("versionId: " + integer + "," + "flag: " + map.get(integer));
        }
        return map;
    }

}
