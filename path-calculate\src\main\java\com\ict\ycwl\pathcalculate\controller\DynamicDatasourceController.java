package com.ict.ycwl.pathcalculate.controller;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.pathcalculate.AOP.ConfigFileMothodDataSource;
import com.ict.ycwl.pathcalculate.mapper.RouteMapper;
import com.ict.ycwl.pathcalculate.pojo.Route;
import com.ict.ycwl.pathcalculate.pojo.Version;
import com.ict.ycwl.pathcalculate.pojo.dto.VersionDTO;
import com.ict.ycwl.pathcalculate.pojo.request.SaveNewVersionRequest;
import com.ict.ycwl.pathcalculate.pojo.vo.VersionDbVo;
import com.ict.ycwl.pathcalculate.pojo.vo.VersionVo;
import com.ict.ycwl.pathcalculate.service.VersionService;
import com.ict.ycwl.pathcalculate.utils.dbDataSourceUtils.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Api(tags = "版本管理API")
@RequestMapping("/dynamicDatasource")
@Slf4j(topic = "c.test")
public class DynamicDatasourceController {

    @Value("${jjking.dbPath}")
    private String dbPath;

    @Autowired
    private RouteMapper routeMapper;


    @Autowired
    private VersionService versionService;

    @Autowired
    private com.ict.ycwl.pathcalculate.utils.VersionDatabaseInitializer versionDatabaseInitializer;


    @ApiOperation(value = "版本切换功能",notes="实际上是切换了使用的数据库，来达到切换版本的目的")
    @GetMapping("/enable/version/{version}")
    @DS("master")
    public AjaxResult enable( @ApiParam(
            name = "version",
            value = "要启用的版本号-可选值：1-系统初始版本，2子系统1，3子系统2，4子系统3",
            example = "1",
            allowableValues="1,2,3,4",
            required = true
    )@PathVariable int version) {
        System.out.println(version);
        //1.根据版本id获取对应实际的数据库名称
        AjaxResult ajaxResult= versionService.selectById(version);
        return ajaxResult;
    }

    @ApiOperation(value = "版本列表显示",notes = "返回值中isShow字段1表示显示给用户看")
    @GetMapping("/versionList")
    public AjaxResult versionList(){
        VersionDbVo versionDbVo = new VersionDbVo();
        List<Version> versions =versionService.selectList();
        List<VersionVo> collect = versions.stream().map(version -> {
            VersionVo versionVo = new VersionVo();
            BeanUtils.copyProperties(version, versionVo);
            Date date = version.getUpdateTime();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            String formattedDate = formatter.format(date);
            versionVo.setUpdateTime(formattedDate);
            return versionVo;
        }).collect(Collectors.toList());

        Map<Integer, Boolean> different = versionService.isDifferent();
        versionDbVo.setVersionVos(collect);
        versionDbVo.setMap(different);

        return AjaxResult.success(versionDbVo);
    }


    /*
     * 实现逻辑：
     *   1.如果当前系统中已经存在三个版本：
     *       1.1 前端让用户选择一个覆盖的版本，并携带版本id，和覆盖信息传递
     *   2.如果当前系统中版本数少于三个：
     *       2.1 直接新增一个版本，后端循环遍历数据库中，所有的子数据库，找到没有被启用的
     *   3.执行数据库切换操作。切换到要增加数据的数据库
     *   4.让前端执行一次保存路径的操作
     * */
    @ApiOperation("版本信息更新")
    @PutMapping("/saveNewVersion")
    @DS("master")
    public AjaxResult saveNewVersion(@RequestBody VersionDTO versionDTO) {
        return versionService.saveNewVersion(versionDTO);
    }

  /*  *//*test*//*
    @ApiOperation("切换数据源")
    @GetMapping("/handoff/{dbName}")
    public AjaxResult handoff(@ApiParam(
            name = "dbName",
            value = "要切换的数据源名称\n\n" +
                    "可选值:\n" +
                    "master: 主数据库\n" +
                    "slave1: 从数据库1\n" +
                    "slave2: 从数据库2\n" +
                    "slave3: 从数据库3",
            example = "master",
            allowableValues = "master,slave1,slave2,slave3",
            required = true
    ) @PathVariable  String dbName) {
        boolean write = FileWriteUtil.writeToFile(dbPath, dbName);
        if(write){
            return AjaxResult.success("切换成功");
        }
        return AjaxResult.error(400,"切换失败");
    }*/

    @ApiOperation("根据路线id获取路线")
    @GetMapping("getRouteById")
    public AjaxResult  getRouteById() {
        Route route = routeMapper.selectOne(new LambdaQueryWrapper<Route>().eq(Route::getRouteId, "1907362626498322434"));
        return AjaxResult.success(route);
    }

    @ApiOperation("删除版本功能")
    @DeleteMapping("/deleteVersion/{versionId}")
    @DS("master")
    public AjaxResult deleteVersion(@PathVariable int versionId){
        if(versionId==1){
            return AjaxResult.error("主版本不能删除");
        }
        String type = FileUtil.readSingleLine(dbPath);
        if("slave1".equals(type)&&versionId==2){
            return AjaxResult.error("不能删除自身版本");
        }
        if("slave2".equals(type)&&versionId==3){
            return AjaxResult.error("不能删除自身版本");
        }
        if("slave3".equals(type)&&versionId==4){
            return AjaxResult.error("不能删除自身版本");
        }
        AjaxResult info= versionService.deleteVersion(versionId);
        return info;
    }

    @ApiOperation(value = "查询当前版本",notes="拿着返回值去匹配versionDb字段，就是当前版本")
    @GetMapping("/getCurrVersion")
    public AjaxResult getCurrVersion(){
        String type = FileUtil.readSingleLine(dbPath);
        return AjaxResult.success(type);
    }

    @ApiOperation(value = "另存为新版本")
    @PostMapping("SaveNewVersion")
    @ConfigFileMothodDataSource(configKey = "jjking.MybatisFile")
    public AjaxResult SaveAsANewVersion(SaveNewVersionRequest request){
        //1.判断当前版本是否已经存满了三个
        Version version=versionService.selectOne();
        //2.如果存满3个版本，让用户删除一个版本
        if(version==null){
            return AjaxResult.error("版本已存满，请删除一个");
        }
        log.debug(version.toString());

        // ===== 保守修复：确保API密钥可用 =====
        String apiKey = request.getApiKey();
        if (apiKey == null || apiKey.trim().isEmpty()) {
            // 使用默认API密钥，与PathController保持一致
            apiKey = "a123fae9da370c45984c58720bf3ac7c";
            log.info("另存为新版本：使用默认API密钥");
        }
        log.debug("另存为新版本使用API密钥: {}...", apiKey.substring(0, 8));

        //执行拷贝方法，将数据拷贝到另一个数据库中
        AjaxResult ajaxResult = versionService.saveNew(version.getVersionDb(), apiKey);
        //判断是否拷贝成功
        if(ajaxResult.isSuccess()){
            log.debug("数据拷贝成功");
            //修改版本基本信息
            version.setVersionName(request.getVersionName());
            version.setVersionInfo(request.getRemake());
            version.setIsShow(1);
            int update= versionService.update(version);
            if(update>0) {
                log.debug("另存新版本成功");
                return AjaxResult.success("另存新版本成功");
            }
        }
        return AjaxResult.error("存新版本失败");
    }

    @ApiOperation(value = "slave1")
    @GetMapping("/slave1/{version}")
    public AjaxResult test(@PathVariable String version){
        return  versionService.saveNew(version);
    }

    @ApiOperation(value="slave2")
    @GetMapping("/slave2/{master}")
    public AjaxResult master(@PathVariable String master){
        return versionService.master(master);
    }


    @ApiOperation("判断当前版本与其他版本是否存在差异")
    @GetMapping("/isDifferent")
    public AjaxResult isDifferent(){
        Map<Integer,Boolean> different = versionService.isDifferent();
        return AjaxResult.success(different);
    }





    @ApiOperation(value = "诊断历史版本功能", notes = "检查数据库配置、表结构、版本数据等")
    @GetMapping("/diagnose")
    public AjaxResult diagnoseVersionFunction() {
        try {
            // 手动触发初始化检查
            versionDatabaseInitializer.manualInitialize();
            return AjaxResult.success("历史版本功能诊断完成，请查看日志获取详细信息");
        } catch (Exception e) {
            log.error("历史版本功能诊断失败", e);
            return AjaxResult.error("诊断失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取当前数据源状态", notes = "显示当前使用的数据源和配置文件内容")
    @GetMapping("/status")
    public AjaxResult getDataSourceStatus() {
        try {
            String currentDs = FileUtil.readSingleLine(dbPath);
            Map<String, Object> status = new HashMap<>();
            status.put("currentDataSource", currentDs);
            status.put("configFilePath", dbPath);
            status.put("configFileExists", new java.io.File(dbPath).exists());

            return AjaxResult.success(status);
        } catch (Exception e) {
            log.error("获取数据源状态失败", e);
            return AjaxResult.error("获取状态失败: " + e.getMessage());
        }
    }

}
