<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3bd2477c-9aff-4bff-b16d-d6cdc034af5e" name="更改" comment="算法成功">
      <change beforePath="$PROJECT_DIR$/path-calculate/src/main/java/com/ict/ycwl/pathcalculate/service/Impl/CalculateServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/src/main/java/com/ict/ycwl/pathcalculate/service/Impl/CalculateServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/application-h3.yml" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/application-h3.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileDataMothodSourceAspect$FileCache.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileDataMothodSourceAspect$FileCache.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileDataMothodSourceAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileDataMothodSourceAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileDataSource.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileDataSource.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileDataSourceAspect$FileCache.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileDataSourceAspect$FileCache.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileDataSourceAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileDataSourceAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileMothodDataSource.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/ConfigFileMothodDataSource.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/DynamicDS.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/DynamicDS.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/DynamicDataSourceAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/AOP/DynamicDataSourceAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/PathCalculateApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/PathCalculateApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/.idea/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/.idea/algorithm.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/.idea/inspectionProfiles/Project_Default.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/.idea/inspectionProfiles/profiles_settings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/.idea/misc.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/.idea/modules.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/.idea/vcs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/PathPlanningUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/PathPlanningUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/AccumulationWeight$AccumulationWeightBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/AccumulationWeight$AccumulationWeightBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/AccumulationWeight.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/AccumulationWeight.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/ConnectionPoint$ConnectionPointBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/ConnectionPoint$ConnectionPointBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/ConnectionPoint.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/ConnectionPoint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/ConstraintValidationResult$ConstraintValidationResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/ConstraintValidationResult$ConstraintValidationResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/ConstraintValidationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/ConstraintValidationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/GeographicCenter$GeographicCenterBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/GeographicCenter$GeographicCenterBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/GeographicCenter.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/GeographicCenter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/IntelligentRouteCountAdjuster.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/IntelligentRouteCountAdjuster.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteAdjustmentResult$RouteAdjustmentResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteAdjustmentResult$RouteAdjustmentResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteAdjustmentResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteAdjustmentResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteMergePair$RouteMergePairBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteMergePair$RouteMergePairBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteMergePair.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteMergePair.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteMergingAlgorithm.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteMergingAlgorithm.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteSplitCandidate$RouteSplitCandidateBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteSplitCandidate$RouteSplitCandidateBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteSplitCandidate.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteSplitCandidate.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteSplittingAlgorithm.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/adjustment/RouteSplittingAlgorithm.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ClusteringPostOptimizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ClusteringPostOptimizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ClusteringPostOptimizerImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ClusteringPostOptimizerImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintAnalyzer$ViolationSeverity.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintAnalyzer$ViolationSeverity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintAnalyzer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintAnalyzer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintViolationReport$ConstraintViolationReportBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintViolationReport$ConstraintViolationReportBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintViolationReport$OptimizationPriority.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintViolationReport$OptimizationPriority.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintViolationReport$ViolationType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintViolationReport$ViolationType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintViolationReport.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ConstraintViolationReport.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/DataTransformationLayer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/DataTransformationLayer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/MultiStrategyOptimizationManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/MultiStrategyOptimizationManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationHistory.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationHistory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationReportExporter.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationReportExporter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationRoundResult$OptimizationRoundResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationRoundResult$OptimizationRoundResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationRoundResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationRoundResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationStrategy$OptimizationType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationStrategy$OptimizationType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationStrategy$ResourceConsumptionLevel.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationStrategy$ResourceConsumptionLevel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationStrategy.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizationStrategy.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizerStatus$ConstraintFixStats$ConstraintFixStatsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizerStatus$ConstraintFixStats$ConstraintFixStatsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizerStatus$ConstraintFixStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizerStatus$ConstraintFixStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizerStatus$OptimizerStatusBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizerStatus$OptimizerStatusBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizerStatus$Status.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizerStatus$Status.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizerStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/OptimizerStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ResultValidator.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ResultValidator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ValidationResult$ValidationCheck.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ValidationResult$ValidationCheck.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ValidationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/core/ValidationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/AdvancedRouteCountEvaluator.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/AdvancedRouteCountEvaluator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolation$ConstraintViolationBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolation$ConstraintViolationBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolation$ViolationSeverity.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolation$ViolationSeverity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolation.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolationAnalysis$ConstraintSatisfactionGrade.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolationAnalysis$ConstraintSatisfactionGrade.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolationAnalysis$ConstraintViolationAnalysisBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolationAnalysis$ConstraintViolationAnalysisBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolationAnalysis.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/ConstraintViolationAnalysis.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis$EfficiencyAnalysisBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis$EfficiencyAnalysisBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis$EfficiencyBottleneck.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis$EfficiencyBottleneck.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis$EfficiencyGrade.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis$EfficiencyGrade.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis$RouteEfficiencyMetrics$RouteEfficiencyMetricsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis$RouteEfficiencyMetrics$RouteEfficiencyMetricsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis$RouteEfficiencyMetrics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis$RouteEfficiencyMetrics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/EfficiencyAnalysis.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$BinPackingAnalysis$BinPackingAnalysisBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$BinPackingAnalysis$BinPackingAnalysisBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$BinPackingAnalysis.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$BinPackingAnalysis.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$LoadBalanceGrade.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$LoadBalanceGrade.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$LoadBalancingAnalysis$LoadBalancingAnalysisBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$LoadBalancingAnalysis$LoadBalancingAnalysisBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$LoadBalancingAnalysis.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$LoadBalancingAnalysis.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$MathematicalModelAnalysisBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$MathematicalModelAnalysisBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$ModelOptimalityGrade.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$ModelOptimalityGrade.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$OptimalityAssessment$OptimalityAssessmentBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$OptimalityAssessment$OptimalityAssessmentBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$OptimalityAssessment.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$OptimalityAssessment.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$PackingQuality.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$PackingQuality.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$QueueingSystemGrade.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$QueueingSystemGrade.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$QueueingTheoryAnalysis$QueueingTheoryAnalysisBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$QueueingTheoryAnalysis$QueueingTheoryAnalysisBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$QueueingTheoryAnalysis.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$QueueingTheoryAnalysis.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$SolutionQuality.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis$SolutionQuality.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/MathematicalModelAnalysis.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountAction.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountAction.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountEvaluation$RouteCountEvaluationBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountEvaluation$RouteCountEvaluationBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountEvaluation.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountEvaluation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$AdjustmentMagnitude.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$AdjustmentMagnitude.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$AdjustmentRisk$AdjustmentRiskBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$AdjustmentRisk$AdjustmentRiskBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$AdjustmentRisk.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$AdjustmentRisk.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$DecisionStrength.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$DecisionStrength.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$DimensionScores$DimensionScoresBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$DimensionScores$DimensionScoresBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$DimensionScores.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$DimensionScores.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$ExpectedImprovement$ExpectedImprovementBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$ExpectedImprovement$ExpectedImprovementBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$ExpectedImprovement.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$ExpectedImprovement.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$ImplementationPriority.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$ImplementationPriority.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$RiskLevel.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$RiskLevel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$RouteCountRecommendationBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation$RouteCountRecommendationBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/RouteCountRecommendation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/WorkloadAnalysis$WorkloadAnalysisBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/WorkloadAnalysis$WorkloadAnalysisBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/WorkloadAnalysis$WorkloadDistributionGrade.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/WorkloadAnalysis$WorkloadDistributionGrade.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/WorkloadAnalysis.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/WorkloadAnalysis.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackAlgorithmManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackAlgorithmManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackAlgorithmStatistics$FallbackAlgorithmStatisticsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackAlgorithmStatistics$FallbackAlgorithmStatisticsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackAlgorithmStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackAlgorithmStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackOptimizationResult$ConvergenceInfo$ConvergenceInfoBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackOptimizationResult$ConvergenceInfo$ConvergenceInfoBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackOptimizationResult$ConvergenceInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackOptimizationResult$ConvergenceInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackOptimizationResult$FallbackOptimizationResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackOptimizationResult$FallbackOptimizationResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackOptimizationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackOptimizationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackStrategy.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/FallbackStrategy.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/GeneticAlgorithmOptimizer$Individual.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/GeneticAlgorithmOptimizer$Individual.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/GeneticAlgorithmOptimizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/GeneticAlgorithmOptimizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/LocalSearchOptimizer$ExchangeCandidate.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/LocalSearchOptimizer$ExchangeCandidate.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/LocalSearchOptimizer$LocalSearchOperation.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/LocalSearchOptimizer$LocalSearchOperation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/LocalSearchOptimizer$LocalSearchResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/LocalSearchOptimizer$LocalSearchResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/LocalSearchOptimizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/LocalSearchOptimizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/OptimizationMetrics$ComparisonResult$ComparisonResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/OptimizationMetrics$ComparisonResult$ComparisonResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/OptimizationMetrics$ComparisonResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/OptimizationMetrics$ComparisonResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/OptimizationMetrics$OptimizationMetricsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/OptimizationMetrics$OptimizationMetricsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/OptimizationMetrics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/OptimizationMetrics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/ProblemCharacteristics$ProblemCharacteristicsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/ProblemCharacteristics$ProblemCharacteristicsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/ProblemCharacteristics$ProblemScale.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/ProblemCharacteristics$ProblemScale.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/ProblemCharacteristics$TimeDistribution.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/ProblemCharacteristics$TimeDistribution.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/ProblemCharacteristics$ViolationSeverity.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/ProblemCharacteristics$ViolationSeverity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/ProblemCharacteristics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/ProblemCharacteristics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/SimulatedAnnealingOptimizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/SimulatedAnnealingOptimizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/VariableNeighborhoodSearch$NeighborhoodType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/VariableNeighborhoodSearch$NeighborhoodType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/VariableNeighborhoodSearch$VNSResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/VariableNeighborhoodSearch$VNSResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/VariableNeighborhoodSearch.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/fallback/VariableNeighborhoodSearch.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/JSPRITVRPOptimizer$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/JSPRITVRPOptimizer$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/JSPRITVRPOptimizer$OptimizationStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/JSPRITVRPOptimizer$OptimizationStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/JSPRITVRPOptimizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/JSPRITVRPOptimizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$AcceptanceStrategy.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$AcceptanceStrategy.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$ConstructionHeuristic.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$ConstructionHeuristic.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$JSPRITParameters$JSPRITParametersBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$JSPRITParameters$JSPRITParametersBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$JSPRITParameters.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$JSPRITParameters.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$RecreateStrategyWeights$RecreateStrategyWeightsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$RecreateStrategyWeights$RecreateStrategyWeightsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$RecreateStrategyWeights.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$RecreateStrategyWeights.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$RuinStrategyWeights$RuinStrategyWeightsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$RuinStrategyWeights$RuinStrategyWeightsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$RuinStrategyWeights.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig$RuinStrategyWeights.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/config/JSPRITAlgorithmConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/converter/VRPProblemConverter.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/converter/VRPProblemConverter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPConstraintValidationResult$VRPConstraintValidationResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPConstraintValidationResult$VRPConstraintValidationResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPConstraintValidationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPConstraintValidationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPConstraints$VRPConstraintsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPConstraints$VRPConstraintsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPConstraints.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPConstraints.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPLocation$LocationType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPLocation$LocationType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPLocation$VRPLocationBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPLocation$VRPLocationBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPLocation.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPLocation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPProblem$VRPProblemBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPProblem$VRPProblemBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPProblem.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPProblem.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPProblemStatistics$VRPProblemStatisticsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPProblemStatistics$VRPProblemStatisticsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPProblemStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPProblemStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPService$ServiceType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPService$ServiceType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPService$VRPServiceBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPService$VRPServiceBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPService.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPValidationResult$VRPValidationResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPValidationResult$VRPValidationResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPValidationResult$ValidationType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPValidationResult$ValidationType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPValidationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPValidationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPVehicle$VRPVehicleBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPVehicle$VRPVehicleBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPVehicle.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/jsprit/domain/VRPVehicle.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ConstraintType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ConstraintType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/LinearConstraint.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/LinearConstraint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPProblem$ObjectiveType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPProblem$ObjectiveType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPProblem$SolutionStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPProblem$SolutionStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPProblem$VariableType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPProblem$VariableType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPProblem.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPProblem.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPSolution$MILPSolutionBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPSolution$MILPSolutionBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPSolution.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPSolution.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPVariable$MILPVariableBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPVariable$MILPVariableBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPVariable.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/MILPVariable.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ObjectiveFunction$ObjectiveFunctionBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ObjectiveFunction$ObjectiveFunctionBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ObjectiveFunction.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ObjectiveFunction.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ProblemStatistics$ProblemStatisticsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ProblemStatistics$ProblemStatisticsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ProblemStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ProblemStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/UnifiedConstraintModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/UnifiedConstraintModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ValidationResult$ValidationResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ValidationResult$ValidationResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ValidationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/ValidationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/ApacheCommonsMathSolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/ApacheCommonsMathSolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/BuiltinHeuristicSolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/BuiltinHeuristicSolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/DefaultSolverSelectionStrategy.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/DefaultSolverSelectionStrategy.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/MILPSolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/MILPSolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverCapabilities$LicenseType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverCapabilities$LicenseType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverCapabilities$SolverCapabilitiesBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverCapabilities$SolverCapabilitiesBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverCapabilities$SolverTier.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverCapabilities$SolverTier.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverCapabilities.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverCapabilities.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverManager$SolverStatistics$SolverStatisticsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverManager$SolverStatistics$SolverStatisticsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverManager$SolverStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverManager$SolverStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverParameters$SolverParametersBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverParameters$SolverParametersBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverParameters$VerbosityLevel.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverParameters$VerbosityLevel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverParameters.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverParameters.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverSelectionStrategy.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverSelectionStrategy.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/OptaPlannerConstraintOptimizer$OptimizationResult$OptimizationResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/OptaPlannerConstraintOptimizer$OptimizationResult$OptimizationResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/OptaPlannerConstraintOptimizer$OptimizationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/OptaPlannerConstraintOptimizer$OptimizationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/OptaPlannerConstraintOptimizer$OptimizationStatistics$OptimizationStatisticsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/OptaPlannerConstraintOptimizer$OptimizationStatistics$OptimizationStatisticsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/OptaPlannerConstraintOptimizer$OptimizationStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/OptaPlannerConstraintOptimizer$OptimizationStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/OptaPlannerConstraintOptimizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/OptaPlannerConstraintOptimizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/AccumulationAssignment.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/AccumulationAssignment.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/Cluster.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/Cluster.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/ClusteringOptimizationSolution.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/ClusteringOptimizationSolution.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/ConstraintWeights$ConstraintWeightsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/ConstraintWeights$ConstraintWeightsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/ConstraintWeights.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/ConstraintWeights.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/OptimizationParameters$ConstraintStreamParallelism.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/OptimizationParameters$ConstraintStreamParallelism.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/OptimizationParameters$ConstructionHeuristicType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/OptimizationParameters$ConstructionHeuristicType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/OptimizationParameters$OptimizationParametersBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/OptimizationParameters$OptimizationParametersBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/OptimizationParameters.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/domain/OptimizationParameters.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/solver/ClusteringSolverConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/solver/ClusteringSolverConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/solver/CoreBusinessConstraintProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/optaplanner/solver/CoreBusinessConstraintProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/实施方案/H3算法终止条件智能化升级方案_20250805.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/实施方案/分层约束优化实施方案_20250803.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/实施方案/工作记录_聚类二次优化实施日志_20250803.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/实施方案/最终技术方案_路线数量评估与约束统一_20250803.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/实施方案/约束条件澄清与技术方案修正_20250803.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/实施方案/重审标准.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/实现日志/X.MD" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/第三方库集成/第三方高性能库选择与集成分析.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/设计方案/聚类二次优化算法架构设计.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/调研报告/Git历史分析报告.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/调研报告/H3六边形网格聚类替代方案_技术可行性分析_20250805.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/调研报告/H3系统集成兼容性分析_20250805.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/调研报告/工作日志深度分析.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/调研报告/技术可行性分析_基于业界最佳实践_20250803.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/调研报告/测试流程分析报告.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/调研报告/聚类二次优化问题分析报告_20250805.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/调研报告/聚类输出数据格式分析.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/项目概述与需求.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/console/log.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdaptiveTSPSolver$AlgorithmPredictor.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdaptiveTSPSolver$AlgorithmPredictor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdaptiveTSPSolver$AlgorithmResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdaptiveTSPSolver$AlgorithmResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdaptiveTSPSolver$ProblemCharacteristics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdaptiveTSPSolver$ProblemCharacteristics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdaptiveTSPSolver$TSPAlgorithm.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdaptiveTSPSolver$TSPAlgorithm.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdaptiveTSPSolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdaptiveTSPSolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager$ConstraintViolationLevel.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager$ConstraintViolationLevel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager$OptimizationStrategy.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager$OptimizationStrategy.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager$ProblemCharacteristics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager$ProblemCharacteristics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager$ProblemComplexity.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager$ProblemComplexity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager$ProblemScale.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager$ProblemScale.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedConstraintOptimizerManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedJNIRecovery.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AdvancedJNIRecovery.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AlgorithmContext.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AlgorithmContext.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AlgorithmParameters.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/AlgorithmParameters.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/BranchAndBoundTSP$TSPNode.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/BranchAndBoundTSP$TSPNode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/BranchAndBoundTSP.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/BranchAndBoundTSP.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ClusteringQualityEvaluator$ClusteringQualityMetrics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ClusteringQualityEvaluator$ClusteringQualityMetrics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ClusteringQualityEvaluator.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ClusteringQualityEvaluator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ConvexHullManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ConvexHullManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/DataPreprocessor.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/DataPreprocessor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/DataValidator.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/DataValidator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/DynamicProgrammingTSP$DPState.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/DynamicProgrammingTSP$DPState.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/DynamicProgrammingTSP.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/DynamicProgrammingTSP.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/EnhancedGeneticTSP$Individual.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/EnhancedGeneticTSP$Individual.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/EnhancedGeneticTSP$Population.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/EnhancedGeneticTSP$Population.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/EnhancedGeneticTSP.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/EnhancedGeneticTSP.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/H3GeographicClustering$BoundingBox.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/H3GeographicClustering$BoundingBox.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/H3GeographicClustering.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/H3GeographicClustering.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/JNIFixService.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/JNIFixService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/JSPRITVRPReoptimizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/JSPRITVRPReoptimizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ManualORToolsLoader.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ManualORToolsLoader.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ManualORToolsTSP.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ManualORToolsTSP.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/MultiObjectiveTSP$ObjectiveWeights.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/MultiObjectiveTSP$ObjectiveWeights.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/MultiObjectiveTSP$OptimizationGoal.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/MultiObjectiveTSP$OptimizationGoal.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/MultiObjectiveTSP$RouteMetrics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/MultiObjectiveTSP$RouteMetrics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/MultiObjectiveTSP$SolutionEvaluation.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/MultiObjectiveTSP$SolutionEvaluation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/MultiObjectiveTSP.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/MultiObjectiveTSP.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsBootstrap.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsBootstrap.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsClassLoadGuard$ClassLoadStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsClassLoadGuard$ClassLoadStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsClassLoadGuard.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsClassLoadGuard.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsTSP$Edge.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsTSP$Edge.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsTSP.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsTSP.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/OptaPlannerVRPReoptimizer$VRPCustomerReoptimization.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/OptaPlannerVRPReoptimizer$VRPCustomerReoptimization.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/OptaPlannerVRPReoptimizer$VRPReoptimizationSolution.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/OptaPlannerVRPReoptimizer$VRPReoptimizationSolution.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/OptaPlannerVRPReoptimizer$VRPVehicleReoptimization.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/OptaPlannerVRPReoptimizer$VRPVehicleReoptimization.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/OptaPlannerVRPReoptimizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/OptaPlannerVRPReoptimizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ReflectiveORToolsTSP.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/ReflectiveORToolsTSP.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/RobustORToolsTSP$ORToolsCapability.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/RobustORToolsTSP$ORToolsCapability.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/RobustORToolsTSP.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/RobustORToolsTSP.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/RouteTimeCalculator$DepotDistanceInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/RouteTimeCalculator$DepotDistanceInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/RouteTimeCalculator.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/RouteTimeCalculator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/SafeORToolsTSP.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/SafeORToolsTSP.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPConstraintEnforcer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPConstraintEnforcer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPPostOptimizationManager$ConstraintViolationAnalysis.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPPostOptimizationManager$ConstraintViolationAnalysis.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPPostOptimizationManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPPostOptimizationManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$BatchSolveRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$BatchSolveRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$SolverPerformanceStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$SolverPerformanceStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$SolverStatusReport.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$SolverStatusReport.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$SolverStrategy.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$SolverStrategy.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$TSPCacheManager$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$TSPCacheManager$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$TSPCacheManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager$TSPCacheManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPSolverManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TerminationDecision$DecisionType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TerminationDecision$DecisionType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TerminationDecision$TerminationDecisionBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TerminationDecision$TerminationDecisionBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TerminationDecision.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TerminationDecision.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TimeBalanceOptimizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TimeBalanceOptimizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TimeBasedTerminationEvaluator.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TimeBasedTerminationEvaluator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TimeEvaluationConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TimeEvaluationConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TimeEvaluationResult$TimeEvaluationResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TimeEvaluationResult$TimeEvaluationResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TimeEvaluationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/TimeEvaluationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/UnifiedClusteringAdapter$ClusteringAlgorithmType.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/UnifiedClusteringAdapter$ClusteringAlgorithmType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/UnifiedClusteringAdapter.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/UnifiedClusteringAdapter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/UnifiedTimeCalculationService.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/UnifiedTimeCalculationService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$AccumulationTransferCandidate.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$AccumulationTransferCandidate.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$AdaptiveConstraints.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$AdaptiveConstraints.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterCenter.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterCenter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterCenterInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterCenterInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterMergeAnalysis.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterMergeAnalysis.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterTimeAnalysis.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterTimeAnalysis.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterTimeInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterTimeInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterWorkTimeInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$ClusterWorkTimeInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$DensityStatistics.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$DensityStatistics.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$GeometricCenter.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$GeometricCenter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$MSTEdge.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$MSTEdge.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$MergeTarget.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$MergeTarget.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$OutlierRelocationCandidate.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$OutlierRelocationCandidate.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$PointCandidate.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$PointCandidate.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$PointPair.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$PointPair.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$PointRole.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$PointRole.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$SmallClusterInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$SmallClusterInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$StageConstraints.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$StageConstraints.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$SwapCandidate.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$SwapCandidate.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TargetClusterOption.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TargetClusterOption.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TransferCandidate.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TransferCandidate.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TransferContext.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TransferContext.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TransferDecision.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TransferDecision.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TransferDecisionResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TransferDecisionResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TransferPair.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$TransferPair.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$UnionFind.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$UnionFind.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$WeightedCenter.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans$WeightedCenter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/core/WorkloadBalancedKMeans.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data-extractor/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data-extractor/config.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data-extractor/data_extraction.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data-extractor/extract_data.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data-extractor/quick_start.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data-extractor/requirements.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/DataLoader$DataValidationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/DataLoader$DataValidationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/DataLoader.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/DataLoader.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/DataLoaderTest.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/DataLoaderTest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/v1.0/accumulations.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/v1.0/data_summary.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/v1.0/teams.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/v1.0/time_matrix.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/data/v1.0/transit_depots.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/debug/DebugDataExporter.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/debug/DebugDataExporter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/debug/ORToolsAdvancedDiagnostic.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/debug/ORToolsAdvancedDiagnostic.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/debug/ORToolsDebugger.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/debug/ORToolsDebugger.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/01-整体架构设计.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/02-文件结构与组织.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/03-数据预处理算法.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/04-负载均衡聚类算法.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/05-TSP求解算法详解.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/06-凸包管理与冲突解决.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/07-时间平衡优化算法.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/08-算法集成与调用流程.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/09-参数调优与性能优化.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/README-算法集成.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/WorkloadBalancedKMeans算法详细说明文档.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/数据提取工具关系一致性优化说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/测试数据格式文档.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/算法设计.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/算法质量评估报告.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/粤北卷烟物流运输规划数据库说明文档.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/doc/路径规划算法接口文档.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/dto/PathPlanningRequest$PathPlanningRequestBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/dto/PathPlanningRequest$PathPlanningRequestBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/dto/PathPlanningRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/dto/PathPlanningRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/dto/PathPlanningResult$PathPlanningResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/dto/PathPlanningResult$PathPlanningResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/dto/PathPlanningResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/dto/PathPlanningResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/Accumulation$AccumulationBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/Accumulation$AccumulationBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/Accumulation.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/Accumulation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/ConflictResolution.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/ConflictResolution.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/CoordinatePoint$CoordinatePointBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/CoordinatePoint$CoordinatePointBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/CoordinatePoint.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/CoordinatePoint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/RouteResult$RouteResultBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/RouteResult$RouteResultBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/RouteResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/RouteResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/Team$TeamBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/Team$TeamBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/Team.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/Team.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeBalanceAdjustment.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeBalanceAdjustment.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeBalanceStats$BalanceGrade.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeBalanceStats$BalanceGrade.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeBalanceStats$TimeBalanceStatsBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeBalanceStats$TimeBalanceStatsBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeBalanceStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeBalanceStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeInfo$TimeInfoBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeInfo$TimeInfoBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TimeInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TransitDepot$TransitDepotBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TransitDepot$TransitDepotBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TransitDepot.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/entity/TransitDepot.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_JVM类初始化缓存问题根本解决方案_20250802_0540.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_OR-Tools测试成功但实际失败的JVM缓存问题分析_20250802_0535.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_OR-Tools类初始化失败完全解决_20250802_0400.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_OR-Tools调查与修复_20250801_2325.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_TSP动态调整策略模式重构_专注TSP本身优化_20250802_0700.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_TSP第三方库优化_20250801_1345.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_TSP算法完整实现总结_20250731_2300.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_TSP算法深度修复与OR-Tools真实可用性实现_20250801_2350.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_TSP编译错误修复_20250731_2330.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_TSP路线序列优化深度分析与改进方案_20250731_2100.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_七大深层问题优化完整实现_20250731_1900.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_七大问题分析优化_20250731_1800.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_三大核心问题深度调查分析_20250731_2340.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_业务逻辑实施缺陷分析与修复_20250726_1700.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_候选多样化修复方案实施_20250728_2330.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_凸包检测过严阻止转移均摊问题调查_20250727_1900.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_单位不匹配导致聚类数错误_公里分钟转换修复_20250727_0230.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_地理划分和转移均摊深度调查_质量指标问题分析_20250727_2230.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_大聚类转移盲区关键修复_20250728_2240.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_完整修复阶段间设计冲突_实现激进转移策略_20250727_1800.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_局部均摊问题根本原因分析与针对性援助方案_20250728_1537.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_拆分策略改进_边缘点转移机制_20250726_1500.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_按用户设计修复拆分合并逻辑_20250727_0900.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_新丰县均摊失效深度调查_20250728_2305.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_时间平衡拆分合并优化_20250725_1440.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_时间平衡拆分合并问题诊断与优化_20250725_2130.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_根本原因调查_拆分结果未重新验证导致过度分散_20250727_1740.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_渐进转移策略三阶段关键修复完成_20250728_2200.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_渐进转移策略失效源码深度分析_20250728_1715.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_渐进转移策略系统性修复方案_20250728_2115.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_激进拆分策略实现_允许临时超出目标聚类数_20250727_0430.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_激进转移策略实现_基于整体方差判断_20250727_0330.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_班组二过度分散问题深度分析_20250727_0830.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_目标聚类不匹配问题关键修复_20250728_2145.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_第三方库集成后约束违反严重问题分析_20250802_0645.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_编译错误修复_20250731_1930.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_编译错误完全修复_20250731_1940.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_聚类数量计算偏小根因分析_时间组成缺失_20250727_0200.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_聚类数量计算错误修复_往返时间逻辑纠正_20250727_0140.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_聚类算法参数优化与凸包约束_20250726_0500.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_自然扩散修复指向性转移缺陷_20250729_1400.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_自适应地理约束放宽机制_20250729_0130.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_负载均衡优化完整修复_20250730_0103.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_边缘点查找算法修复_多重条件优化_20250727_0500.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_过度拆分问题诊断与修复_20250726_1430.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/工作日志_迭代聚类数计算策略实现_20250727_0300.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/log/问题分析.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/sql/ycdb_str.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/utils/ConvexHullGenerator.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/utils/ConvexHullGenerator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/algorithm/workload_changes.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/common/exception/ApiKeyException.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/common/exception/ApiKeyException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/common/exception/CarNumberException.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/common/exception/CarNumberException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/AlgorithmConfig$AlgorithmParams.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/AlgorithmConfig$AlgorithmParams.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/AlgorithmConfig$ExecutionMode.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/AlgorithmConfig$ExecutionMode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/AlgorithmConfig$LogLevel.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/AlgorithmConfig$LogLevel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/AlgorithmConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/AlgorithmConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/AsyncConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/AsyncConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/JacksonObjectMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/JacksonObjectMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/MybatisPlusConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/MybatisPlusConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/SwaggerConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/SwaggerConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/TransactionConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/TransactionConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/WebMvcConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/config/WebMvcConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/controller/AsyncController.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/controller/AsyncController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/controller/NewAlgorithmController.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/controller/NewAlgorithmController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/controller/PathController.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/controller/PathController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/filter/CustomDataSourceFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/filter/CustomDataSourceFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/form/AddRouteFrom.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/form/AddRouteFrom.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/form/AdjustPointForm.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/form/AdjustPointForm.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/form/GetColourConvexHullFrom.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/form/GetColourConvexHullFrom.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/form/RouteDataForm.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/form/RouteDataForm.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/AccumulationMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/AccumulationMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/AreaMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/AreaMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/CarMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/CarMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/DistMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/DistMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/GearMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/GearMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/GroupAreasMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/GroupAreasMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/GroupMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/GroupMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/PointDistanceMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/PointDistanceMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/RouteDetailMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/RouteDetailMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/RouteMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/RouteMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/StoreMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/StoreMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/StoreTimeMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/StoreTimeMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/SystemParameterMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/SystemParameterMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/TransitDepotMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/TransitDepotMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/VersionMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/mapper/VersionMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Accumulation$AccumulationBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Accumulation$AccumulationBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Accumulation.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Accumulation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Area$AreaBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Area$AreaBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Area.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Area.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/AveTimeWorkTime.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/AveTimeWorkTime.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Car.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Car.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Dist.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Dist.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/DoublePoint.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/DoublePoint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Gear.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Gear.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Group.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Group.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/GroupAreas.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/GroupAreas.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/LngAndLat$LatitudeComparator.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/LngAndLat$LatitudeComparator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/LngAndLat$LongitudeComparator.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/LngAndLat$LongitudeComparator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/LngAndLat.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/LngAndLat.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Point2DAop.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Point2DAop.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/PointDistance.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/PointDistance.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/ResultRoute.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/ResultRoute.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Route$RouteBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Route$RouteBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Route.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Route.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/RouteDetail$RouteDetailBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/RouteDetail$RouteDetailBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/RouteDetail.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/RouteDetail.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Store$StoreBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Store$StoreBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Store.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Store.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/StoreTime.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/StoreTime.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/SystemParameter.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/SystemParameter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/TransitDepot.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/TransitDepot.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Version.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/Version.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/WorkTimeUpdateRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/WorkTimeUpdateRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/WorkTimeVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/WorkTimeVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dto/VersionDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dto/VersionDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/AccumulationF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/AccumulationF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/AreaF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/AreaF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/CarDailyInformationF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/CarDailyInformationF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/CarF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/CarF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/CenterDistanceF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/CenterDistanceF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/DeliveryAreaF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/DeliveryAreaF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/DeliveryTypeF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/DeliveryTypeF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/DistF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/DistF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/ErrorPointF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/ErrorPointF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/FeedbackF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/FeedbackF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/FeedbackFileF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/FeedbackFileF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/FeedbackReplyF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/FeedbackReplyF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/FeedbackReplyFileF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/FeedbackReplyFileF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/FileImportLogsF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/FileImportLogsF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/GearF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/GearF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/GroupAreasF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/GroupAreasF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/GroupF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/GroupF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/OperationF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/OperationF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/PickupUserF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/PickupUserF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/PickupUserImportF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/PickupUserImportF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/PickupUserParameterF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/PickupUserParameterF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/PointDistanceF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/PointDistanceF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/RoleF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/RoleF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/RoleOperationF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/RoleOperationF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/RouteDetailF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/RouteDetailF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/RouteF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/RouteF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/RouteUserF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/RouteUserF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SchedulingF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SchedulingF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SchedulingUser.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SchedulingUser.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SecondTransitF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SecondTransitF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SiteSelectionF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SiteSelectionF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SiteStoreF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SiteStoreF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/StoreF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/StoreF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/StoreTimeF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/StoreTimeF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/StoreTwoF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/StoreTwoF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SystemParameterF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/SystemParameterF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/TeamF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/TeamF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/TransitDeliveryF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/TransitDeliveryF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/TransitDepotCarF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/TransitDepotCarF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/TransitDepotF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/TransitDepotF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/UserF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/UserF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/UserGroupF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/UserGroupF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/VersionF.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/dynamiEntity/VersionF.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/vo/VersionDbVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/vo/VersionDbVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/vo/VersionVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/pojo/vo/VersionVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/AsyncService.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/AsyncService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/CalculateService.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/CalculateService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/CalculateServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/CalculateServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/MapDisplayServiceImpl$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/MapDisplayServiceImpl$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/MapDisplayServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/MapDisplayServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteDetailServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteDetailServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/SaveVersionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/SaveVersionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/SystemParameterServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/Impl/SystemParameterServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/MapDisplayService.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/MapDisplayService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/NewAlgorithmService.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/NewAlgorithmService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/RouteDetailService.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/RouteDetailService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/RouteService.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/RouteService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/SystemParameterService.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/service/SystemParameterService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/AccumulationUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/AccumulationUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/AreaCenterPoint.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/AreaCenterPoint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/BoundaryPoint.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/BoundaryPoint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/ConvexHull.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/ConvexHull.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/FileOutputUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/FileOutputUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/FindNearestBoundaryPoint.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/FindNearestBoundaryPoint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/MySQLConnection.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/MySQLConnection.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/TSPUtils$Point.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/TSPUtils$Point.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/TSPUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/TSPUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/VersionDatabaseInitializer.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/VersionDatabaseInitializer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/dbDataSourceUtils/DataSourceContextHolder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/dbDataSourceUtils/DataSourceContextHolder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/dbDataSourceUtils/DynamicDataSource.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/dbDataSourceUtils/DynamicDataSource.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/dbDataSourceUtils/FileUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/dbDataSourceUtils/FileUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/dbDataSourceUtils/FileWriteUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/dbDataSourceUtils/FileWriteUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Edge_cutting.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Edge_cutting.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Main.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Main.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Networkfulling.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Networkfulling.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Readboundary.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Readboundary.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Readroute.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Readroute.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Test.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Test.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Triangulation.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/Triangulation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/VoronoiSplit.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/getColorUtils/VoronoiSplit.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/pathOptimization/GeneticAlgorithm.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/pathOptimization/GeneticAlgorithm.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/pathOptimization/SpeciesIndividual.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/pathOptimization/SpeciesIndividual.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/pathOptimization/SpeciesPopulation.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/pathOptimization/SpeciesPopulation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/pathOptimization/TSPData.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/utils/pathOptimization/TSPData.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/ConvexPointVO$ConvexPointVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/ConvexPointVO$ConvexPointVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/ConvexPointVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/ConvexPointVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/GroupDataVO$GroupDataVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/GroupDataVO$GroupDataVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/GroupDataVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/GroupDataVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/GroupRouteVO$GroupRouteVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/GroupRouteVO$GroupRouteVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/GroupRouteVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/GroupRouteVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/RouteDataVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/RouteDataVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/RouteVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/RouteVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/TransitDepotRouteVO$TransitDepotRouteVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/TransitDepotRouteVO$TransitDepotRouteVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/TransitDepotRouteVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/TransitDepotRouteVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/TransitDepotVO$TransitDepotVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/TransitDepotVO$TransitDepotVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/TransitDepotVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/TransitDepotVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/AccumulationDetailsVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/AccumulationDetailsVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/RouteDateVO$RouteDateVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/RouteDateVO$RouteDateVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/RouteDateVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/RouteDateVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/RouteDetailsVO$RouteDetailsVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/RouteDetailsVO$RouteDetailsVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/RouteDetailsVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/RouteDetailsVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/StoreDetailsVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/StoreDetailsVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/TransitDepotDetailsVO$TransitDepotDetailsVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/TransitDepotDetailsVO$TransitDepotDetailsVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/TransitDepotDetailsVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/classes/com/ict/ycwl/pathcalculate/vo/details/TransitDepotDetailsVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/path-calculate/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/algorithm/data/v1.0/accumulations.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/algorithm/data/v1.0/data_summary.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/algorithm/data/v1.0/teams.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/algorithm/data/v1.0/time_matrix.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/algorithm/data/v1.0/transit_depots.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/application-test.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/RouteQualityAnalyzer$Coordinate.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/RouteQualityAnalyzer$DeliveryTime.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/RouteQualityAnalyzer$RouteAnalysis.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/RouteQualityAnalyzer$RouteData.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/RouteQualityAnalyzer$SegmentTime.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/RouteQualityAnalyzer.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/AdvancedORToolsRecoveryTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ClassInitializationPollutionTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/CleanORToolsValidationTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ClusteringPostOptimizationTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/FinalVerificationTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/FixedORToolsTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/JNIDiagnosticTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ManualORToolsFixTest$TestData.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ManualORToolsFixTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsDeepDiagnose.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsDiagnosticTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsIntegrationTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsJNIFixTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsLibraryExtractionTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsNativeLibCheck.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsRealTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsSimpleTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsSimplifiedTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsStaticInitTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/ORToolsSystemDiagnose.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/PathPlanningAlgorithmTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/PathPlanningTestRunner.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/PathPlanningUtilsSimpleTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/PathPlanningUtilsTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/QuickDiagnosticTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/QuickORToolsIntegrationTest$TestData.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/QuickORToolsIntegrationTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/QuickTestRunner.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/RealAlgorithmORToolsTest$TestData.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/RealAlgorithmORToolsTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/RunPathPlanningTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/SafeORToolsTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/evaluation/AdvancedRouteCountEvaluatorTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverManagerTest$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverManagerTest$MockSolver.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/clustering_post_optimization/milp/solver/SolverManagerTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/core/ORToolsFixedTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/core/TSPThirdPartyLibraryDiagnosticTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/algorithm/测试类使用指南.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/controller/PathControllerDatabaseLockTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/com/ict/ycwl/pathcalculate/integration/NewAlgorithmIntegrationTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/path-calculate/target/test-classes/logback-test.xml" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="JUnit5 Test Class" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/data-management/target/app.jar!/BOOT-INF/classes/mapper/CarMapper.xml" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="$PROJECT_DIR$/../../../../maven/apache-maven-3.6.3" />
        <option name="showDialogWithAdvancedSettings" value="true" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="disabledProfiles">
      <list>
        <option value="development" />
      </list>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="1.8 (2)" />
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2mvV0xk9HrVo4mmWzPRpimDtwZl" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrary&quot;: &quot;JUnit5&quot;,
    &quot;com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrarySuperClass.JUnit5&quot;: &quot;&quot;,
    &quot;database.data.extractors.current.export.id&quot;: &quot;Markdown-Groovy.md.groovy&quot;,
    &quot;deletionFromPopupRequiresConfirmation&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;grid.search.filter.rows&quot;: &quot;true&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;1.8&quot;,
    &quot;last_directory_selection&quot;: &quot;C:/Users/<USER>/Desktop/烟草/交接信息/源代码/ycwl-ms-v3.0/path-calculate/src/test/java&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/烟草/交接信息/源代码/ycwl-ms-v3.0&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDK&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.424626&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;configurable.group.language&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;99a8ba2511bfe88f7ebc6ab27cea7821&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\烟草\\交接信息\\源代码\\ycwl-ms-v3.0\\first\\test\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;ChangesTree.GroupingKeys&quot;: [
      &quot;directory&quot;,
      &quot;module&quot;
    ],
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\src\main\java\com\ict\ycwl\pathcalculate" />
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\path-calculate" />
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\user-service\src\main\java\com\ict\ycwl\user\utils" />
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\user-service\src\main\java\com\ict\ycwl\user" />
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\pickup\src\main\java\com\ict\ycwl\pickup\utils" />
    </key>
    <key name="CreateTestDialog.Recents.Supers">
      <recent name="" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\path-calculate\src\main\resources" />
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\path-calculate\src\main\resources\mapper" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.ict.ycwl.pathcalculate" />
      <recent name="com.ict.ycwl.clustercalculate.utlis" />
      <recent name="com.ict.ycwl.clustercalculate.pojo" />
      <recent name="com.ict.ycwl.clustercalculate" />
      <recent name="com.ict.ycwl.pathcalculate.service.Impl" />
    </key>
    <key name="CreateTestDialog.RecentsKey">
      <recent name="com.ict.ycwl.pathcalculate.utils" />
      <recent name="com.ict.ycwl.clustercalculate.service.Impl" />
      <recent name="com.ict.ycwl.pathcalculate.service.Impl" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.ict.ycwl.guestbook.filter" />
      <recent name="com.ict.ycwl.pathcalculate.AOP" />
      <recent name="com.ict.ycwl.clustercalculate" />
      <recent name="com.ict.ycwl.clustercalculate.pojo" />
      <recent name="com.ict.ycwl.clustercalculate.mapper" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.GatewayApplication">
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RouteTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="pathcalculate" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.pathcalculate.route.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ict.ycwl.pathcalculate.route" />
      <option name="MAIN_CLASS_NAME" value="com.ict.ycwl.pathcalculate.route.RouteTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RouteTest001.test06" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="pathcalculate" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.pathcalculate.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ict.ycwl.pathcalculate" />
      <option name="MAIN_CLASS_NAME" value="com.ict.ycwl.pathcalculate.RouteTest001" />
      <option name="METHOD_NAME" value="test06" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RouteTest001.test06" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="pathcalculate" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.pathcalculate.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ict.ycwl.pathcalculate" />
      <option name="MAIN_CLASS_NAME" value="com.ict.ycwl.pathcalculate.RouteTest001" />
      <option name="METHOD_NAME" value="test06" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RouteTest001.test07" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="pathcalculate" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.pathcalculate.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ict.ycwl.pathcalculate" />
      <option name="MAIN_CLASS_NAME" value="com.ict.ycwl.pathcalculate.RouteTest001" />
      <option name="METHOD_NAME" value="test07" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RouteTest001.test07" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="pathcalculate" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.pathcalculate.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ict.ycwl.pathcalculate" />
      <option name="MAIN_CLASS_NAME" value="com.ict.ycwl.pathcalculate.RouteTest001" />
      <option name="METHOD_NAME" value="test07" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RouteTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="pathcalculate" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.pathcalculate.route.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ict.ycwl.pathcalculate.route" />
      <option name="MAIN_CLASS_NAME" value="com.ict.ycwl.pathcalculate.route.RouteTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ClusterCalculateApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="clustercalculate" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.clustercalculate.ClusterCalculateApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ClusterCalculateApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="clustercalculate" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.clustercalculate.ClusterCalculateApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DataManagementApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8 (2)" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="data-management" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.datamanagement.DataManagementApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DataManagementApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8 (2)" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="data-management" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.datamanagement.DataManagementApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="gateway" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.gateway.GatewayApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.gateway.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="gateway" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.gateway.GatewayApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.gateway.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GuestbookApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="guestbook" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.guestbook.GuestbookApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GuestbookApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="guestbook" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.guestbook.GuestbookApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PathCalculateApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="pathcalculate" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.pathcalculate.PathCalculateApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PathCalculateApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="pathcalculate" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.pathcalculate.PathCalculateApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PickupServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="pickup" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.pickup.PickupServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PickupServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="pickup" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.pickup.PickupServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="user-service" />
      <option name="PROGRAM_PARAMETERS" value="-Dserver.port=8081" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.user.UserServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="user-service" />
      <option name="PROGRAM_PARAMETERS" value="-Dserver.port=8081" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.user.UserServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="user-service" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.user.UserServiceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.user.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="user-service" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.user.UserServiceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.user.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8 (2)" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="user-service" />
      <option name="PROGRAM_PARAMETERS" value="-Dserver.port=8081" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.user.UserServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.RouteTest001.test06" />
      <item itemvalue="JUnit.RouteTest001.test07" />
      <item itemvalue="JUnit.RouteTest" />
      <item itemvalue="Spring Boot.ClusterCalculateApplication" />
      <item itemvalue="Spring Boot.DataManagementApplication" />
      <item itemvalue="Spring Boot.GuestbookApplication" />
      <item itemvalue="Spring Boot.PathCalculateApplication" />
      <item itemvalue="Spring Boot.PickupServiceApplication" />
      <item itemvalue="Spring Boot.UserServiceApplication (1)" />
      <item itemvalue="Spring Boot.GatewayApplication" />
      <item itemvalue="Spring Boot.UserServiceApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.GatewayApplication" />
        <item itemvalue="Spring Boot.UserServiceApplication" />
        <item itemvalue="JUnit.RouteTest001.test06" />
        <item itemvalue="JUnit.RouteTest" />
        <item itemvalue="JUnit.RouteTest001.test07" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3bd2477c-9aff-4bff-b16d-d6cdc034af5e" name="更改" comment="" />
      <created>1712155953319</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1712155953319</updated>
      <workItem from="1727956924120" duration="6492000" />
      <workItem from="1728009894252" duration="2923000" />
      <workItem from="1728025096680" duration="10912000" />
      <workItem from="1728091366135" duration="46567000" />
      <workItem from="1728384561877" duration="24327000" />
      <workItem from="1728517371435" duration="1598000" />
      <workItem from="1728518990669" duration="6063000" />
      <workItem from="1728606316375" duration="31376000" />
      <workItem from="1728735541893" duration="6015000" />
      <workItem from="1728782604798" duration="53879000" />
      <workItem from="1729061232028" duration="7501000" />
      <workItem from="1729083289705" duration="1110000" />
      <workItem from="1729122750011" duration="5959000" />
      <workItem from="1729170569043" duration="5293000" />
      <workItem from="1729210876155" duration="13214000" />
      <workItem from="1729241464291" duration="13872000" />
      <workItem from="1729301288860" duration="55826000" />
      <workItem from="1729481458987" duration="4220000" />
      <workItem from="1729509760565" duration="52896000" />
      <workItem from="1729900262002" duration="33336000" />
      <workItem from="1730001598471" duration="31757000" />
      <workItem from="1730084352115" duration="4814000" />
      <workItem from="1730099235784" duration="19563000" />
      <workItem from="1730176288488" duration="797000" />
      <workItem from="1730177360052" duration="1378000" />
      <workItem from="1730178898907" duration="866000" />
      <workItem from="1730187322587" duration="13344000" />
      <workItem from="1730272299763" duration="62879000" />
      <workItem from="1730513446254" duration="22285000" />
      <workItem from="1730596155611" duration="40502000" />
      <workItem from="1730815338347" duration="18963000" />
      <workItem from="1730963258643" duration="59223000" />
      <workItem from="1731215227001" duration="39472000" />
      <workItem from="1731407466747" duration="17606000" />
      <workItem from="1731484944818" duration="17871000" />
      <workItem from="1731550283936" duration="874000" />
      <workItem from="1731583058080" duration="44445000" />
      <workItem from="1731690880321" duration="2981000" />
      <workItem from="1731694047934" duration="322000" />
      <workItem from="1731694401042" duration="2647000" />
      <workItem from="1731697133369" duration="27828000" />
      <workItem from="1731848271632" duration="2915000" />
      <workItem from="1731858634289" duration="23000" />
      <workItem from="1731927400644" duration="38403000" />
      <workItem from="1732191545642" duration="7695000" />
      <workItem from="1732201057000" duration="25326000" />
      <workItem from="1732456431714" duration="474000" />
      <workItem from="1732518434267" duration="26729000" />
      <workItem from="1732618622584" duration="9828000" />
      <workItem from="1732670187637" duration="26149000" />
      <workItem from="1732799882970" duration="177000" />
      <workItem from="1732800071884" duration="3316000" />
      <workItem from="1732841596078" duration="1383000" />
      <workItem from="1732843535742" duration="26989000" />
      <workItem from="1732891794663" duration="25000" />
      <workItem from="1732967659953" duration="11459000" />
      <workItem from="1732983675174" duration="3417000" />
      <workItem from="1733013310357" duration="59940000" />
      <workItem from="1733156471436" duration="50823000" />
      <workItem from="1733357045796" duration="28987000" />
      <workItem from="1733444762608" duration="21693000" />
      <workItem from="1733546580500" duration="23395000" />
      <workItem from="1733663130694" duration="12501000" />
      <workItem from="1733728283477" duration="7392000" />
      <workItem from="1733741798452" duration="7937000" />
      <workItem from="1733766005035" duration="602000" />
      <workItem from="1733834612020" duration="654000" />
      <workItem from="1733904786683" duration="7000" />
      <workItem from="1734362895311" duration="77000" />
      <workItem from="1734363063916" duration="618000" />
      <workItem from="1734426647928" duration="2726000" />
      <workItem from="1734437784457" duration="2755000" />
      <workItem from="1734492229425" duration="2349000" />
      <workItem from="1734926521971" duration="7097000" />
      <workItem from="1735179146521" duration="1610000" />
      <workItem from="1735286137486" duration="251000" />
      <workItem from="1735966131744" duration="2413000" />
      <workItem from="1736043542621" duration="730000" />
      <workItem from="1736044591902" duration="3930000" />
      <workItem from="1736396233156" duration="37000" />
      <workItem from="1736473923131" duration="1378000" />
      <workItem from="1736946109307" duration="39000" />
      <workItem from="1737595894419" duration="13598000" />
      <workItem from="1737768144699" duration="252000" />
      <workItem from="1737768500707" duration="45000" />
      <workItem from="1737768850478" duration="309000" />
      <workItem from="1738131227913" duration="646000" />
      <workItem from="1738379674101" duration="4521000" />
      <workItem from="1738546876842" duration="1811000" />
      <workItem from="1738637084958" duration="5184000" />
      <workItem from="1738656424669" duration="1021000" />
      <workItem from="1738819817611" duration="851000" />
      <workItem from="1738899000446" duration="25428000" />
      <workItem from="1739070199883" duration="48493000" />
      <workItem from="1739412662659" duration="59162000" />
      <workItem from="1739673420929" duration="47330000" />
      <workItem from="1740019906393" duration="142000" />
      <workItem from="1740020946720" duration="1745000" />
      <workItem from="1740052325004" duration="3579000" />
      <workItem from="1740102366491" duration="8983000" />
      <workItem from="1740189709634" duration="6247000" />
      <workItem from="1740278262416" duration="783000" />
      <workItem from="1740312100607" duration="3152000" />
      <workItem from="1740376296539" duration="8469000" />
      <workItem from="1740400196326" duration="6705000" />
      <workItem from="1740492111788" duration="22000" />
      <workItem from="1740531357497" duration="54363000" />
      <workItem from="1740802094933" duration="66000" />
      <workItem from="1740802173115" duration="15493000" />
      <workItem from="1741000371117" duration="7270000" />
      <workItem from="1741007698844" duration="5675000" />
      <workItem from="1741074450032" duration="726000" />
      <workItem from="1741088107551" duration="51093000" />
      <workItem from="1741342738741" duration="16011000" />
      <workItem from="1741396618126" duration="31971000" />
      <workItem from="1741507072025" duration="26898000" />
      <workItem from="1741703516259" duration="23966000" />
      <workItem from="1741824767236" duration="9983000" />
      <workItem from="1741867641188" duration="1038000" />
      <workItem from="1741917953761" duration="9481000" />
      <workItem from="1742102274296" duration="11481000" />
      <workItem from="1742115144579" duration="6000" />
      <workItem from="1742176424065" duration="379000" />
      <workItem from="1742176984744" duration="22128000" />
      <workItem from="1742297230298" duration="86474000" />
      <workItem from="1742820911383" duration="49698000" />
      <workItem from="1743039820460" duration="205000" />
      <workItem from="1743040329550" duration="622000" />
      <workItem from="1743150217715" duration="4967000" />
      <workItem from="1743332659137" duration="1355000" />
      <workItem from="1743335613021" duration="9821000" />
      <workItem from="1743421071092" duration="219000" />
      <workItem from="1743421308625" duration="9608000" />
      <workItem from="1743473430577" duration="53236000" />
      <workItem from="1743985805936" duration="48775000" />
      <workItem from="1744262266304" duration="19870000" />
      <workItem from="1744614710708" duration="373000" />
      <workItem from="1744724410247" duration="7415000" />
      <workItem from="1744797528381" duration="5285000" />
      <workItem from="1744878083020" duration="411000" />
      <workItem from="1745025361367" duration="28875000" />
      <workItem from="1745916114936" duration="2000" />
      <workItem from="1745931314825" duration="41000" />
      <workItem from="1746017381450" duration="330000" />
      <workItem from="1746599298452" duration="732000" />
      <workItem from="1746600914592" duration="24625000" />
      <workItem from="1746942882327" duration="37569000" />
      <workItem from="1747274402650" duration="38913000" />
      <workItem from="1748145006844" duration="16876000" />
      <workItem from="1748413419486" duration="493000" />
      <workItem from="1748415674013" duration="1544000" />
      <workItem from="1748435916766" duration="5164000" />
      <workItem from="1748441174468" duration="733000" />
      <workItem from="1748441922954" duration="3303000" />
      <workItem from="1748477627723" duration="24271000" />
      <workItem from="1748931121401" duration="1145000" />
      <workItem from="1749020830609" duration="221000" />
      <workItem from="1749106978131" duration="88000" />
      <workItem from="1749107085500" duration="35804000" />
      <workItem from="1749775727210" duration="29937000" />
      <workItem from="1750330557175" duration="77356000" />
      <workItem from="1750917807267" duration="110000" />
      <workItem from="1751006752172" duration="9182000" />
      <workItem from="1751078521155" duration="638000" />
      <workItem from="1751289102876" duration="5846000" />
      <workItem from="1751340550062" duration="3559000" />
      <workItem from="1751368027691" duration="7831000" />
      <workItem from="1751418007313" duration="2700000" />
      <workItem from="1751436197806" duration="14045000" />
      <workItem from="1751504261401" duration="2546000" />
      <workItem from="1751547755367" duration="1163000" />
      <workItem from="1751634173510" duration="80989000" />
      <workItem from="1751852355693" duration="14894000" />
      <workItem from="1751940408403" duration="10023000" />
      <workItem from="1751983139401" duration="3913000" />
      <workItem from="1752026723945" duration="40000" />
      <workItem from="1752026777554" duration="58000" />
      <workItem from="1752026852512" duration="12867000" />
      <workItem from="1752064395492" duration="4546000" />
      <workItem from="1752113857519" duration="18677000" />
      <workItem from="1752156259773" duration="84000" />
      <workItem from="1752156364429" duration="1604000" />
      <workItem from="1752196198102" duration="19240000" />
      <workItem from="1752392713988" duration="16418000" />
      <workItem from="1752454921099" duration="18292000" />
      <workItem from="1752541885878" duration="19839000" />
      <workItem from="1752580500112" duration="6216000" />
      <workItem from="1752628551741" duration="8820000" />
      <workItem from="1752646039328" duration="6896000" />
      <workItem from="1752673871147" duration="353000" />
      <workItem from="1752716571441" duration="16559000" />
      <workItem from="1752743953867" duration="3731000" />
      <workItem from="1752803851662" duration="33000" />
      <workItem from="1752809273604" duration="20822000" />
      <workItem from="1752986121148" duration="7743000" />
      <workItem from="1753146665914" duration="718000" />
      <workItem from="1753172084498" duration="3842000" />
      <workItem from="1753209657751" duration="776000" />
      <workItem from="1753252055033" duration="8020000" />
      <workItem from="1753339906174" duration="11549000" />
      <workItem from="1753355160819" duration="2895000" />
      <workItem from="1753509140323" duration="3052000" />
      <workItem from="1753512325510" duration="12502000" />
      <workItem from="1753525532759" duration="3460000" />
      <workItem from="1753529145881" duration="1350000" />
      <workItem from="1753530506942" duration="2448000" />
      <workItem from="1753546818236" duration="13241000" />
      <workItem from="1753588978507" duration="3477000" />
      <workItem from="1753677345268" duration="12359000" />
      <workItem from="1753699431779" duration="2961000" />
      <workItem from="1753721667757" duration="7651000" />
      <workItem from="1753730861501" duration="125000" />
      <workItem from="1753762689887" duration="4233000" />
      <workItem from="1753860895691" duration="1196000" />
      <workItem from="1753874501354" duration="3364000" />
      <workItem from="1753891842113" duration="317000" />
      <workItem from="1753892392531" duration="170000" />
      <workItem from="1753892771706" duration="7242000" />
      <workItem from="1753909746797" duration="5071000" />
      <workItem from="1753924773625" duration="2000" />
      <workItem from="1753945307029" duration="8237000" />
      <workItem from="1753958515585" duration="3895000" />
      <workItem from="1753962525796" duration="2225000" />
      <workItem from="1754039611750" duration="22274000" />
      <workItem from="1754109883875" duration="950000" />
      <workItem from="1754205999261" duration="14598000" />
      <workItem from="1754239694731" duration="8486000" />
      <workItem from="1754281778684" duration="734000" />
      <workItem from="1754297261556" duration="6056000" />
      <workItem from="1754390576776" duration="1207000" />
      <workItem from="1754415909474" duration="6715000" />
      <workItem from="1754456583069" duration="620000" />
      <workItem from="1754552018102" duration="3935000" />
      <workItem from="1754556262227" duration="25000" />
      <workItem from="1754556367536" duration="41000" />
      <workItem from="1754556433826" duration="24000" />
      <workItem from="1754556533538" duration="7134000" />
      <workItem from="1754574964777" duration="1355000" />
      <workItem from="1754576393092" duration="7478000" />
      <workItem from="1754630298604" duration="658000" />
      <workItem from="1754679806463" duration="3546000" />
      <workItem from="1754730724763" duration="2030000" />
      <workItem from="1754733637422" duration="745000" />
      <workItem from="1754735221153" duration="4669000" />
      <workItem from="1754741621547" duration="61000" />
      <workItem from="1754741693974" duration="5007000" />
      <workItem from="1754748128929" duration="1477000" />
      <workItem from="1754751402756" duration="3243000" />
      <workItem from="1754756982715" duration="56000" />
      <workItem from="1754757179471" duration="2349000" />
      <workItem from="1754759702073" duration="9000" />
      <workItem from="1754762982939" duration="2632000" />
      <workItem from="1754841763092" duration="6962000" />
      <workItem from="1754850010505" duration="2427000" />
      <workItem from="1754894937629" duration="9134000" />
      <workItem from="1754907711840" duration="5850000" />
      <workItem from="1754918122525" duration="6349000" />
      <workItem from="1754933576331" duration="538000" />
      <workItem from="1754937935365" duration="4019000" />
      <workItem from="1754974001391" duration="1943000" />
      <workItem from="1755085532561" duration="2047000" />
      <workItem from="1755089948629" duration="2521000" />
    </task>
    <task id="LOCAL-00001" summary="初次推送项目到github">
      <created>1737768996003</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1737768996004</updated>
    </task>
    <task id="LOCAL-00002" summary="创建顶点取货模块，并创建取货列表实体类">
      <created>1738641812575</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1738641812575</updated>
    </task>
    <task id="LOCAL-00003" summary="定点取货功能基本已经完成，但可能还存在一个bug待修复">
      <created>1740020020196</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1740020020196</updated>
    </task>
    <task id="LOCAL-00004" summary="定点取货功能基本已经完成，但可能还存在一个bug待修复">
      <created>1740020042218</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1740020042218</updated>
    </task>
    <task id="LOCAL-00005" summary="修复大量bug">
      <created>1740555126155</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1740555126155</updated>
    </task>
    <task id="LOCAL-00006" summary="完成大部分班组内路径优化，但是工作时长本来较长的班组的优化结果部分路线的工作时长会超过8小时">
      <created>1741678896199</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1741678896199</updated>
    </task>
    <task id="LOCAL-00007" summary="等待甲方验收版本，但是仍然存在部分bug">
      <created>1743214912983</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1743214912983</updated>
    </task>
    <task id="LOCAL-00008" summary="代码进行初步的sql优化，计算能正常完成，但仍需要较长的时间">
      <created>1743517517901</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1743517517901</updated>
    </task>
    <task id="LOCAL-00009" summary="基本上没有什么bug">
      <created>1745124769108</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1745124769108</updated>
    </task>
    <task id="LOCAL-00010" summary="基本上没有什么bug">
      <created>1745124859404</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1745124859404</updated>
    </task>
    <task id="LOCAL-00011" summary="引入数据库管理工具liquibase前进行备份">
      <created>1748137399343</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1748137399343</updated>
    </task>
    <task id="LOCAL-00012" summary="增加了商铺列表中的状态字段">
      <created>1748444057715</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1748444057715</updated>
    </task>
    <task id="LOCAL-00013" summary="实现版本控制之前进行数据库备份">
      <created>1749110004033</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1749110004033</updated>
    </task>
    <task id="LOCAL-00014" summary="引入版本控制前进行备份">
      <created>1749692987923</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1749692987923</updated>
    </task>
    <task id="LOCAL-00015" summary="存在循环依赖问题">
      <created>1749696597971</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1749696597971</updated>
    </task>
    <task id="LOCAL-00016" summary="实现版本控制功能，已经完成大部分版本管理功能">
      <created>1750310115406</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1750310115406</updated>
    </task>
    <task id="LOCAL-00017" summary="实现凸包不会冲突，但是，工作时长还是不够均衡">
      <created>1750660957655</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1750660957655</updated>
    </task>
    <task id="LOCAL-00018" summary="中转站一能实现挺好的班组均衡，但有一点点凸包冲突，整体上不错，但是中转站2效果很差">
      <created>1750917897462</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1750917897462</updated>
    </task>
    <task id="LOCAL-00019" summary="引入mysqlBaits多数据源前进行备份">
      <created>1751293264347</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1751293264347</updated>
    </task>
    <task id="LOCAL-00020" summary="通过mybatisPlus实现多数据源切换，通过aop自定义注解读取本地文件源实现">
      <created>1751460866340</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1751460866340</updated>
    </task>
    <task id="LOCAL-00021" summary="完成另存为新版本功能">
      <created>1751691542791</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1751691542791</updated>
    </task>
    <task id="LOCAL-00022" summary="完善了导出路径的商铺增加修改状态">
      <created>1751714176409</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1751714176409</updated>
    </task>
    <task id="LOCAL-00023" summary="完成了另存为新版本，已经一些bug修复，部署了聚集区模块的多数据库，pickup模块，data模块的多数据库">
      <created>1751877991874</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1751877991874</updated>
    </task>
    <task id="LOCAL-00024" summary="新增版本间的差异检测">
      <created>1752048934573</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1752048934573</updated>
    </task>
    <task id="LOCAL-00025" summary="交接路径计算">
      <created>1752646905554</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1752646905554</updated>
    </task>
    <task id="LOCAL-00026" summary="基本无bug">
      <created>1752745247163</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1752745247164</updated>
    </task>
    <task id="LOCAL-00042" summary="聚集区bug修改前">
      <option name="closed" value="true" />
      <created>1754060719103</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1754060719103</updated>
    </task>
    <task id="LOCAL-00043" summary="聚集区bug修改前">
      <option name="closed" value="true" />
      <created>1754060774893</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1754060774893</updated>
    </task>
    <task id="LOCAL-00044" summary="聚集区打卡点修复完成">
      <option name="closed" value="true" />
      <created>1754067872652</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1754067872652</updated>
    </task>
    <task id="LOCAL-00045" summary="聚集区打卡点修复完成">
      <option name="closed" value="true" />
      <created>1754067898805</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1754067898805</updated>
    </task>
    <task id="LOCAL-00046" summary="聚集区打卡点修复完成">
      <option name="closed" value="true" />
      <created>1754067919203</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1754067919203</updated>
    </task>
    <task id="LOCAL-00047" summary="聚集区打卡点修复完成（真）">
      <option name="closed" value="true" />
      <created>1754068487547</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1754068487548</updated>
    </task>
    <task id="LOCAL-00048" summary="微调功能成功">
      <option name="closed" value="true" />
      <created>1754246787796</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1754246787796</updated>
    </task>
    <task id="LOCAL-00049" summary="一键调整功能成功">
      <option name="closed" value="true" />
      <created>1754247397713</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1754247397713</updated>
    </task>
    <task id="LOCAL-00050" summary="8.6聚集区计算调整前版本">
      <option name="closed" value="true" />
      <created>1754416181711</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1754416181712</updated>
    </task>
    <task id="LOCAL-00051" summary="密码权限修改完成">
      <option name="closed" value="true" />
      <created>1754421320132</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1754421320132</updated>
    </task>
    <task id="LOCAL-00052" summary="聚集区重新计算成功（？）版本">
      <option name="closed" value="true" />
      <created>1754423669124</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1754423669124</updated>
    </task>
    <task id="LOCAL-00053" summary="聚集区重新计算成功（？）版本">
      <option name="closed" value="true" />
      <created>1754552974557</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1754552974558</updated>
    </task>
    <task id="LOCAL-00054" summary="聚集区重新计算成功版本">
      <option name="closed" value="true" />
      <created>1754552993119</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1754552993119</updated>
    </task>
    <task id="LOCAL-00055" summary="取货重新计算成功">
      <option name="closed" value="true" />
      <created>1754555484497</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1754555484497</updated>
    </task>
    <task id="LOCAL-00056" summary="交接算法前版本">
      <option name="closed" value="true" />
      <created>1754573309572</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1754573309574</updated>
    </task>
    <task id="LOCAL-00057" summary="算法交接开始版本">
      <option name="closed" value="true" />
      <created>1754576464844</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1754576464844</updated>
    </task>
    <task id="LOCAL-00058" summary="算法对接进度70%">
      <option name="closed" value="true" />
      <created>1754758410011</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1754758410011</updated>
    </task>
    <task id="LOCAL-00059" summary="对接完成，开始修改bug">
      <option name="closed" value="true" />
      <created>1754844667137</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1754844667137</updated>
    </task>
    <task id="LOCAL-00060" summary="算法对接完毕">
      <option name="closed" value="true" />
      <created>1754906250300</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1754906250300</updated>
    </task>
    <task id="LOCAL-00061" summary="对接成功（可能）">
      <option name="closed" value="true" />
      <created>1754910382320</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1754910382321</updated>
    </task>
    <task id="LOCAL-00062" summary="对接成功（可能）">
      <option name="closed" value="true" />
      <created>1754914354583</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1754914354583</updated>
    </task>
    <task id="LOCAL-00063" summary="历史版本修复成功">
      <option name="closed" value="true" />
      <created>1754938584730</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1754938584731</updated>
    </task>
    <task id="LOCAL-00064" summary="算法成功">
      <option name="closed" value="true" />
      <created>1754942069395</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1754942069395</updated>
    </task>
    <option name="localTasksCounter" value="65" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="60c2f0be-6321-4abe-8677-f65d3e473a68" value="TOOL_WINDOW" />
        <entry key="9991fc6f-8d44-4f92-a53a-db23f910a08b" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="60c2f0be-6321-4abe-8677-f65d3e473a68">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/烟草/交接信息/源代码/ycwl-ms-v3.0/cluster-calculate" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="9991fc6f-8d44-4f92-a53a-db23f910a08b">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/烟草/交接信息/源代码/ycwl-ms-v3.0/gateway" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="绕远路问题解决后的，数据单位问题解决" />
    <MESSAGE value="权限bug修改前" />
    <MESSAGE value="权限bug修改完成（待前端对接）" />
    <MESSAGE value="用户管理模块bug修改完成" />
    <MESSAGE value="完成商铺导入表格修改（仍存在所在大区的bug）" />
    <MESSAGE value="完成修复大部分bug，车辆功能修改前" />
    <MESSAGE value="正常使用版本" />
    <MESSAGE value="聚集区bug修改前" />
    <MESSAGE value="聚集区打卡点修复完成" />
    <MESSAGE value="聚集区打卡点修复完成（真）" />
    <MESSAGE value="微调功能成功" />
    <MESSAGE value="一键调整功能成功" />
    <MESSAGE value="8.6聚集区计算调整前版本" />
    <MESSAGE value="密码权限修改完成" />
    <MESSAGE value="聚集区重新计算成功（？）版本" />
    <MESSAGE value="聚集区重新计算成功版本" />
    <MESSAGE value="取货重新计算成功" />
    <MESSAGE value="交接算法前版本" />
    <MESSAGE value="算法交接开始版本" />
    <MESSAGE value="算法对接进度70%" />
    <MESSAGE value="对接完成，开始修改bug" />
    <MESSAGE value="算法对接完毕" />
    <MESSAGE value="对接成功（可能）" />
    <MESSAGE value="历史版本修复成功" />
    <MESSAGE value="算法成功" />
    <option name="LAST_COMMIT_MESSAGE" value="算法成功" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/path-calculate/src/test/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImplTest.java</url>
          <line>98</line>
          <option name="timeStamp" value="201" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/path-calculate/src/test/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImplTest.java</url>
          <line>107</line>
          <option name="timeStamp" value="219" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>77</line>
          <option name="timeStamp" value="301" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>84</line>
          <option name="timeStamp" value="312" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>78</line>
          <option name="timeStamp" value="313" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>79</line>
          <option name="timeStamp" value="314" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>80</line>
          <option name="timeStamp" value="315" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>81</line>
          <option name="timeStamp" value="316" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pickup/src/main/java/com/ict/ycwl/pickup/service/impl/PickupUserServiceImpl.java</url>
          <line>437</line>
          <option name="timeStamp" value="367" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/data-management/src/main/java/com/ict/datamanagement/service/impl/DeliveryServiceImpl.java</url>
          <line>299</line>
          <option name="timeStamp" value="383" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1442</line>
          <option name="timeStamp" value="386" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1446</line>
          <option name="timeStamp" value="390" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1447</line>
          <option name="timeStamp" value="391" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1450</line>
          <option name="timeStamp" value="392" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1453</line>
          <option name="timeStamp" value="393" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1461</line>
          <option name="timeStamp" value="394" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1471</line>
          <option name="timeStamp" value="395" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/data-management/src/main/java/com/ict/datamanagement/service/impl/DeliveryServiceImpl.java</url>
          <line>420</line>
          <option name="timeStamp" value="396" />
        </line-breakpoint>
        <breakpoint enabled="true" type="java-exception">
          <properties class="java.lang.RuntimeException" package="java.lang" />
          <option name="timeStamp" value="397" />
        </breakpoint>
        <breakpoint enabled="true" type="java-exception">
          <properties class="java.lang.IllegalArgumentException" package="java.lang" />
          <option name="timeStamp" value="398" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="java.lang.String" memberName="hash" />
        <PinnedItemInfo parentTag="org.springframework.dao.CannotAcquireLockException" memberName="detailMessage" />
        <PinnedItemInfo parentTag="com.ict.datamanagement.domain.entity.Team" memberName="deliveryAreaName" />
        <PinnedItemInfo parentTag="org.apache.commons.math3.ml.clustering.CentroidCluster" memberName="points" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>